<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meenoe - Register</title>
    <meta name="description" content="Create your Meenoe account">
    
    <!-- Custom Bootstrap CSS -->
    <link rel="stylesheet" href="/src/assets/css/bootstrap.css">
    <!-- Tabler icons -->
    <link rel="stylesheet" href="/src/assets/css/tabler.css">
    <!-- Custom Styles -->
    <link rel="stylesheet" href="/src/assets/css/styles.css">
</head>
<body>
    <div class="auth-container d-flex align-items-center justify-content-center">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-4">
                    <div class="card auth-card border-0 rounded-4 shadow-lg">
                        <div class="card-body p-5">
                            <div class="text-center mb-4">
                                <div class="d-flex justify-content-center align-items-center mb-3">
                                    <img src="/src/assets/dark-logo.svg" alt="Meenoe" style="height: 40px;">
                                </div>
                                <h1 class="h4 mb-1">Create your account</h1>
                                <p class="text-muted">Join Meenoe to start collaborating</p>
                            </div>

                            <div id="error-message" class="alert alert-danger d-none">
                                <i class="ti ti-alert-circle" style="width: 20px; height: 20px;" class="me-2"></i>
                                <span id="error-text"></span>
                            </div>

                            <div id="success-message" class="alert alert-success d-none">
                                <i class="ti ti-check" style="width: 20px; height: 20px;" class="me-2"></i>
                                <span id="success-text"></span>
                            </div>

                            <form id="register-form">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Full Name</label>
                                    <input type="text" class="form-control form-control-lg" id="name" placeholder="Enter your full name" required>
                                </div>

                                <div class="mb-3">
                                    <label for="email" class="form-label">Email address</label>
                                    <input type="email" class="form-control form-control-lg" id="email" placeholder="Enter your email" required>
                                </div>

                                <div class="mb-3">
                                    <label for="password" class="form-label">Password</label>
                                    <div class="position-relative">
                                        <input type="password" class="form-control form-control-lg" id="password" placeholder="Create a password" required>
                                        <button type="button" class="btn btn-link position-absolute end-0 top-50 translate-middle-y" id="toggle-password">
                                            <i class="ti ti-eye" style="width: 20px; height: 20px;"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="confirmPassword" class="form-label">Confirm Password</label>
                                    <div class="position-relative">
                                        <input type="password" class="form-control form-control-lg" id="confirmPassword" placeholder="Confirm your password" required>
                                        <button type="button" class="btn btn-link position-absolute end-0 top-50 translate-middle-y" id="toggle-confirm-password">
                                            <i class="ti ti-eye" style="width: 20px; height: 20px;"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="terms" required>
                                        <label class="form-check-label" for="terms">
                                            I agree to the <a href="/terms.html" class="text-decoration-none">Terms of Service</a> and 
                                            <a href="/privacy.html" class="text-decoration-none">Privacy Policy</a>
                                        </label>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary btn-lg w-100 mb-3" id="register-btn">
                                    <span class="btn-text">Create account</span>
                                    <span class="spinner-border spinner-border-sm me-2 d-none" role="status"></span>
                                </button>

                                <div class="text-center">
                                    <span class="text-muted">Already have an account? </span>
                                    <a href="/login.html" class="text-decoration-none">Sign in</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/src/assets/js/bootstrap.js"></script>
    <script src="/src/js/auth.js"></script>
    <script src="/src/js/pages/register.js"></script>
</body>
</html>