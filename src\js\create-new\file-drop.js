/**
 * Initializes the file dropzone functionality on a given container.
 * This function should be called after the dropzone HTML is rendered to the DOM.
 * @param {string} dropAreaId The ID of the main drop area element.
 * @param {string} fileInputId The ID of the hidden file input element.
 * @param {string} fileListContainerId The ID of the container for the file list.
 */
function initFileDropzone(dropAreaId, fileInputId, fileListContainerId) {
    const fileInput = document.getElementById(fileInputId);
    const fileListContainer = document.getElementById(fileListContainerId);
    
    // Check if the core elements exist. If not, we can't proceed.
    if (!document.getElementById(dropAreaId) || !fileInput || !fileListContainer) {
        // console.warn('File dropzone elements not found. Initialization skipped.');
        return;
    }

    // --- Helper Functions ---
    const preventDefaults = (e) => {
        e.preventDefault();
        e.stopPropagation();
    };

    const highlight = (element) => {
        if (element) {
            element.classList.add('border-primary', 'bg-light');
        }
    };

    const unhighlight = (element) => {
        if (element) {
            element.classList.remove('border-primary', 'bg-light');
        }
    };

    const formatFileSize = (bytes) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
    };

    const handleFiles = (files) => {
        [...files].forEach(file => {
            const listItem = document.createElement('div');
            listItem.className = 'list-group-item d-flex justify-content-between align-items-center';
            
            // Add a data attribute to easily find this file later if needed
            // listItem.dataset.fileName = file.name;

            listItem.innerHTML = `
                <div>
                    <span>${file.name}</span><br>
                    <small class="text-muted">${formatFileSize(file.size)}</small>
                </div>
                <button type="button" class="btn btn-sm btn-outline-danger remove-file-btn" aria-label="Remove">
                    <i class="bi bi-x" style="pointer-events: none;"></i>
                </button>
            `;
            fileListContainer.appendChild(listItem);
        });
    };

    // --- Event Delegation Setup on Document ---

    // 1. Prevent browser from opening dropped files globally
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        document.body.addEventListener(eventName, preventDefaults, false);
    });

    // 2. Handle highlighting of the drop area
    ['dragenter', 'dragover'].forEach(eventName => {
        document.addEventListener(eventName, (e) => {
            const dropArea = e.target.closest(`#${dropAreaId}`);
            highlight(dropArea);
        }, false);
    });

    // 3. Handle un-highlighting of the drop area
    ['dragleave', 'drop'].forEach(eventName => {
        document.addEventListener(eventName, (e) => {
            const dropArea = e.target.closest(`#${dropAreaId}`);
            unhighlight(dropArea);
        }, false);
    });
    
    // 4. Handle the actual file drop
    document.addEventListener('drop', (e) => {
        const dropArea = e.target.closest(`#${dropAreaId}`);
        if (dropArea) {
            const dt = e.dataTransfer;
            const files = dt.files;
            handleFiles(files);
        }
    }, false);

    // 5. Delegate click to open file dialog and to remove files
    document.addEventListener('click', (e) => {
        const dropArea = e.target.closest(`#${dropAreaId}`);
        const removeBtn = e.target.closest('.remove-file-btn');

        if (removeBtn) {
            // If a remove button was clicked, remove its parent list item
            removeBtn.parentElement.remove();
            return; // Stop further execution
        }

        if (dropArea) {
            // If the drop area itself was clicked (and not a remove button inside it),
            // trigger the hidden file input.
            fileInput.click();
        }
    });

    // 6. Listen for changes on the file input directly (this is fine as it's scoped to this init)
    fileInput.addEventListener('change', function() {
        if (this.files) {
            handleFiles(this.files);
            // Optional: clear the input value to allow re-uploading the same file
            this.value = ''; 
        }
    });
}