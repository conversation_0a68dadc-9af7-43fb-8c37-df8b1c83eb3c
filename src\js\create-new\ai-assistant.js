// Enhanced AI Assistant with Event Delegation and MutationObserver fallback
(function() {
    'use strict';
    
    let isAssistantVisible = false;
    let aiButton = null;
    let isInitialized = false;
    
    // Initialize the AI Assistant functionality
    function initializeAIAssistant() {
        if (isInitialized) return;
        
        console.log('🤖 Initializing AI Assistant...');
        
        // Set up event delegation first (works immediately)
        setupEventDelegation();
        
        // Try to find the button immediately
        aiButton = document.getElementById('aiAssistantButton');
        
        if (aiButton) {
            console.log('✅ AI Assistant button found immediately');
            setupButtonFeatures(aiButton);
            isInitialized = true;
        } else {
            console.log('⏳ AI Assistant button not found, setting up observer...');
            // Set up MutationObserver as fallback
            setupMutationObserver();
        }
    }
    
    // Set up event delegation on document body
    function setupEventDelegation() {
        console.log('🎯 Setting up event delegation...');
        
        document.body.addEventListener('click', function(e) {
            const clickedButton = e.target.closest('#aiAssistantButton');
            if (clickedButton) {
                e.preventDefault();
                console.log('🖱️ AI Assistant button clicked via delegation');
                
                // Ensure button reference is current
                aiButton = clickedButton;
                
                // Remove pulse on first interaction
                removePulse();
                
                // Toggle assistant panel
                toggleAssistantPanel();
            }
        });
        
        // Handle mouseenter for pulse removal
        document.body.addEventListener('mouseenter', function(e) {
            const hoveredButton = e.target.closest('#aiAssistantButton');
            if (hoveredButton) {
                console.log('🖱️ AI Assistant button hovered via delegation');
                aiButton = hoveredButton;
                removePulse();
            }
        }, true); // Use capture phase for better detection
        
        // Handle Enter key press when button is focused
        document.body.addEventListener('keydown', function(e) {
            const focusedButton = e.target.closest('#aiAssistantButton');
            if (focusedButton && (e.key === 'Enter' || e.key === ' ')) {
                e.preventDefault();
                aiButton = focusedButton;
                removePulse();
                toggleAssistantPanel();
            }
        });
    }
    
    // Set up MutationObserver to watch for button creation
    function setupMutationObserver() {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // Check if the added node is the button or contains the button
                        const foundButton = node.id === 'aiAssistantButton' ? node : node.querySelector && node.querySelector('#aiAssistantButton');
                        
                        if (foundButton) {
                            console.log('✅ AI Assistant button detected via MutationObserver');
                            aiButton = foundButton;
                            setupButtonFeatures(aiButton);
                            observer.disconnect();
                            isInitialized = true;
                        }
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        console.log('👀 MutationObserver active, watching for button...');
    }
    
    // Set up button-specific features like pulse animation
    function setupButtonFeatures(button) {
        if (!button) return;
        
        console.log('⚡ Setting up button features...');
        
        // Add pulse animation
        button.classList.add('pulse');
        
        // Add accessibility attributes if missing
        if (!button.getAttribute('aria-label')) {
            button.setAttribute('aria-label', 'AI Assistant');
        }
        
        if (!button.getAttribute('role')) {
            button.setAttribute('role', 'button');
        }
        
        // Make focusable if not already
        if (!button.getAttribute('tabindex')) {
            button.setAttribute('tabindex', '0');
        }
    }
    
    // Remove pulse animation
    function removePulse() {
        if (aiButton && aiButton.classList.contains('pulse')) {
            console.log('✨ Removing pulse animation');
            aiButton.classList.remove('pulse');
        }
    }
    
    // Toggle AI Assistant Panel
    function toggleAssistantPanel() {
        isAssistantVisible = !isAssistantVisible;
        console.log(`${isAssistantVisible ? '🔓' : '🔒'} Toggling assistant panel: ${isAssistantVisible ? 'SHOW' : 'HIDE'}`);
        
        if (isAssistantVisible) {
            showAssistantPanel();
        } else {
            hideAssistantPanel();
        }
    }
    
    function showAssistantPanel() {
        // Create the panel if it doesn't exist
        let panel = document.getElementById('aiAssistantPanel');
        
        if (!panel) {
            console.log('🛠️ Creating AI Assistant panel...');
            panel = createAssistantPanel();
        }
        
        // Show the panel with animation
        panel.style.display = 'block';
        // Force reflow before adding show class
        panel.offsetHeight;
        setTimeout(() => {
            panel.classList.add('show');
            console.log('📱 AI Assistant panel shown');
        }, 10);
    }
    
    function hideAssistantPanel() {
        const panel = document.getElementById('aiAssistantPanel');
        if (panel) {
            console.log('📱 Hiding AI Assistant panel...');
            panel.classList.remove('show');
            setTimeout(() => {
                if (panel && panel.parentNode) {
                    panel.style.display = 'none';
                }
            }, 300);
        }
    }
    
    function createAssistantPanel() {
        const panel = document.createElement('div');
        panel.id = 'aiAssistantPanel';
        panel.className = 'ai-assistant-panel';
        panel.innerHTML = `
            <div class="ai-assistant-header">
                <h5>AI Assistant</h5>
                <button class="btn-close" aria-label="Close AI Assistant" type="button"></button>
            </div>
            <div class="ai-assistant-body">
                <div class="ai-messages" role="log" aria-live="polite" aria-label="Chat messages">
                    <div class="ai-message ai-message-bot">
                        <div class="ai-avatar" aria-hidden="true">🤖</div>
                        <div class="ai-message-content">
                            <p>Hi there! How can I assist you with your meeting today?</p>
                        </div>
                    </div>
                </div>
                <div class="ai-input-container">
                    <input type="text" 
                           class="form-control" 
                           placeholder="Ask me anything..."
                           aria-label="Type your message"
                           autocomplete="off">
                    <button class="btn btn-primary" 
                            type="button"
                            aria-label="Send message">
                        <i class="ti ti-send" aria-hidden="true"></i>
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(panel);
        console.log('🏗️ AI Assistant panel created and added to DOM');
        
        // Set up panel event listeners
        setupPanelEventListeners(panel);
        
        return panel;
    }
    
    function setupPanelEventListeners(panel) {
        // Close button
        const closeButton = panel.querySelector('.btn-close');
        closeButton.addEventListener('click', function() {
            console.log('❌ Close button clicked');
            hideAssistantPanel();
            isAssistantVisible = false;
        });
        
        // Input and send functionality
        const input = panel.querySelector('input');
        const sendButton = panel.querySelector('.btn-primary');
        
        const sendMessage = () => {
            const message = input.value.trim();
            if (message) {
                console.log('💬 Sending message:', message);
                
                // Add user message
                addMessage('user', message);
                input.value = '';
                
                // Show typing indicator
                showTypingIndicator();
                
                // Simulate AI response (replace with actual API call)
                setTimeout(() => {
                    hideTypingIndicator();
                    const responses = [
                        "I can help you with that. Let me check the details...",
                        "I've analyzed your request. Here's what I found...",
                        "Great question! Here's some information that might help...",
                        "I understand you're looking for assistance with this.",
                        "Let me process that information for you...",
                        "Based on your request, here's what I recommend..."
                    ];
                    const randomResponse = responses[Math.floor(Math.random() * responses.length)];
                    addMessage('bot', randomResponse);
                }, Math.random() * 2000 + 1000); // Random delay between 1-3 seconds
            }
        };
        
        sendButton.addEventListener('click', sendMessage);
        
        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
        
        // Focus the input after a short delay
        setTimeout(() => {
            input.focus();
            console.log('🎯 Input focused');
        }, 100);
        
        // Handle Escape key to close panel
        panel.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                hideAssistantPanel();
                isAssistantVisible = false;
                // Return focus to the AI button
                if (aiButton) {
                    aiButton.focus();
                }
            }
        });
    }
    
    function addMessage(sender, text) {
        const messagesContainer = document.querySelector('.ai-messages');
        if (messagesContainer) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `ai-message ai-message-${sender}`;
            messageDiv.innerHTML = `
                <div class="ai-avatar" aria-hidden="true">${sender === 'bot' ? '🤖' : '👤'}</div>
                <div class="ai-message-content">
                    <p>${escapeHtml(text)}</p>
                </div>
            `;
            messagesContainer.appendChild(messageDiv);
            
            // Scroll to bottom
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            console.log(`💬 Message added (${sender}):`, text);
        }
    }
    
    function showTypingIndicator() {
        const messagesContainer = document.querySelector('.ai-messages');
        if (messagesContainer) {
            const typingDiv = document.createElement('div');
            typingDiv.className = 'ai-message ai-message-bot ai-typing';
            typingDiv.innerHTML = `
                <div class="ai-avatar" aria-hidden="true">🤖</div>
                <div class="ai-message-content">
                    <p class="typing-indicator">
                        <span></span>
                        <span></span>
                        <span></span>
                    </p>
                </div>
            `;
            messagesContainer.appendChild(typingDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
    }
    
    function hideTypingIndicator() {
        const typingIndicator = document.querySelector('.ai-typing');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }
    
    // Utility function to escape HTML
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    // Initialize when DOM is ready or immediately if already ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeAIAssistant);
    } else {
        // DOM is already ready
        initializeAIAssistant();
    }
    
    // Expose utility functions globally for debugging
    window.AIAssistant = {
        isVisible: () => isAssistantVisible,
        toggle: toggleAssistantPanel,
        show: showAssistantPanel,
        hide: hideAssistantPanel,
        getButton: () => aiButton,
        isInitialized: () => isInitialized
    };
    
    console.log('🚀 AI Assistant script loaded');
})();