// Main application initialization
document.addEventListener('DOMContentLoaded', function() {
    // Check authentication
    if (!window.auth.requireAuth()) {
        return;
    }

    // Update user info in sidebar
    updateUserInfo();

    // Setup logout handlers
    setupLogoutHandlers();

    // Setup sidebar toggle
    setupSidebarToggle();

    // Register routes
    registerRoutes();

    // Initialize router
    window.router.handleRoute();
});

function updateUserInfo() {
    const user = window.auth.getUser();
    if (user) {
        const userNameElement = document.getElementById('user-name');
        if (userNameElement) {
            userNameElement.textContent = user.name;
        }

        // Update profile images
        const profileImages = document.querySelectorAll('.client-profile-img img');
        profileImages.forEach(img => {
            if (user.avatar) {
                img.src = user.avatar;
            }
        });
    }
}

function setupLogoutHandlers() {
    const logoutBtns = document.querySelectorAll('#logout-btn, #header-logout-btn');
    logoutBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            window.auth.logout();
            window.location.href = '/login.html';
        });
    });
}

function setupSidebarToggle() {
    const sidebarTogglers = document.querySelectorAll('.sidebartoggler');
    const sidebar = document.querySelector('.left-sidebar');
    const mainWrapper = document.getElementById('main-wrapper');

    sidebarTogglers.forEach(toggler => {
        toggler.addEventListener('click', function() {
            if (window.innerWidth <= 1199) {
                // Mobile: toggle sidebar visibility
                sidebar.classList.toggle('show');
            } else {
                // Desktop: toggle sidebar collapse
                sidebar.classList.toggle('collapsed');
                if (sidebar.classList.contains('collapsed')) {
                    mainWrapper.setAttribute('data-sidebartype', 'mini-sidebar');
                } else {
                    mainWrapper.setAttribute('data-sidebartype', 'full');
                }
            }
        });
    });

    // Close sidebar on mobile when clicking outside
    document.addEventListener('click', function(e) {
        if (window.innerWidth <= 1199) {
            const isClickInsideSidebar = sidebar.contains(e.target);
            const isClickOnToggler = e.target.closest('.sidebartoggler');
            
            if (!isClickInsideSidebar && !isClickOnToggler && sidebar.classList.contains('show')) {
                sidebar.classList.remove('show');
            }
        }
    });
}

function registerRoutes() {
    // Register all page routes
    window.router.addRoute('dashboard', loadDashboard);
    window.router.addRoute('my-meenoes', loadMyMeenoes);
    window.router.addRoute('create-new', loadCreateNew);
    window.router.addRoute('my-files', loadMyFiles);
    window.router.addRoute('organization', loadOrganization);
    window.router.addRoute('notifications', loadNotifications);
    window.router.addRoute('settings', loadSettings);
    window.router.addRoute('support', loadSupport);
    window.router.addRoute('profile', loadProfile);
}

// Placeholder functions for other pages (to be implemented)
function loadMyMeenoes() {
    document.getElementById('main-content').innerHTML = `
        <div class="fade-in">
            <h1 class="h3 mb-4">My Meenoes</h1>
            <p class="text-muted">This page is under construction. Coming soon!</p>
        </div>
    `;
}

function loadMyFiles() {
    document.getElementById('main-content').innerHTML = `
        <div class="fade-in">
            <h1 class="h3 mb-4">My Files</h1>
            <p class="text-muted">This page is under construction. Coming soon!</p>
        </div>
    `;
}

function loadOrganization() {
    document.getElementById('main-content').innerHTML = `
        <div class="fade-in">
            <h1 class="h3 mb-4">Organization</h1>
            <p class="text-muted">This page is under construction. Coming soon!</p>
        </div>
    `;
}

function loadNotifications() {
    document.getElementById('main-content').innerHTML = `
        <div class="fade-in">
            <h1 class="h3 mb-4">Notifications</h1>
            <p class="text-muted">This page is under construction. Coming soon!</p>
        </div>
    `;
}

function loadSettings() {
    document.getElementById('main-content').innerHTML = `
        <div class="fade-in">
            <h1 class="h3 mb-4">Settings</h1>
            <p class="text-muted">This page is under construction. Coming soon!</p>
        </div>
    `;
}

function loadSupport() {
    document.getElementById('main-content').innerHTML = `
        <div class="fade-in">
            <h1 class="h3 mb-4">Support</h1>
            <p class="text-muted">This page is under construction. Coming soon!</p>
        </div>
    `;
}

function loadProfile() {
    document.getElementById('main-content').innerHTML = `
        <div class="fade-in">
            <h1 class="h3 mb-4">Profile</h1>
            <p class="text-muted">This page is under construction. Coming soon!</p>
        </div>
    `;
}