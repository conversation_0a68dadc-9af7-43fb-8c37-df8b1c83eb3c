/**
 * <PERSON><PERSON>e Details Tab Functionality
 * Handles interactive editing of meenoe name, objective, and other details
 */

class MeenoeDetails {
    constructor() {
        this.isInitialized = false;
        this.editingStates = {
            name: false,
            objective: false
        };
        
        this.init();
    }
    
    init() {
        console.log('🔄 Initializing Meenoe Details functionality...');
        
        // Setup event delegation for dynamic content
        this.setupEventDelegation();
        
        // Initialize existing elements if they exist
        this.initializeElements();
        
        this.isInitialized = true;
        console.log('✅ Meenoe Details functionality initialized');
    }
    
    /**
     * Setup event delegation for all meenoe details interactions
     */
    setupEventDelegation() {
        // Use event delegation on document for dynamic content
        document.addEventListener('click', (e) => {
            // Handle edit meenoe name button
            if (e.target.closest('#edit-meenoe-name')) {
                e.preventDefault();
                this.toggleNameEdit();
                return;
            }
            
            // Handle edit objective button
            if (e.target.closest('#edit-objective')) {
                e.preventDefault();
                this.toggleObjectiveEdit();
                return;
            }
            
            // <PERSON>le clicks outside editable elements to save
            if (this.editingStates.name || this.editingStates.objective) {
                const nameElement = document.getElementById('meenoe-name');
                const objectiveElement = document.getElementById('objective-text');
                
                // Check if click is outside the editing elements
                if (nameElement && this.editingStates.name && 
                    !nameElement.contains(e.target) && 
                    !e.target.closest('#edit-meenoe-name')) {
                    this.saveNameEdit();
                }
                
                if (objectiveElement && this.editingStates.objective && 
                    !objectiveElement.contains(e.target) && 
                    !e.target.closest('#edit-objective')) {
                    this.saveObjectiveEdit();
                }
            }
        });
        
        // Handle keyboard events
        document.addEventListener('keydown', (e) => {
            // Save on Enter key
            if (e.key === 'Enter') {
                if (this.editingStates.name && e.target.closest('#meenoe-name')) {
                    e.preventDefault();
                    this.saveNameEdit();
                    return;
                }
                
                if (this.editingStates.objective && e.target.closest('#objective-text')) {
                    e.preventDefault();
                    this.saveObjectiveEdit();
                    return;
                }
            }
            
            // Cancel on Escape key
            if (e.key === 'Escape') {
                if (this.editingStates.name) {
                    this.cancelNameEdit();
                }
                if (this.editingStates.objective) {
                    this.cancelObjectiveEdit();
                }
            }
        });
    }
    
    /**
     * Initialize existing elements
     */
    initializeElements() {
        // Set initial values from state if available
        if (window.meenoeState) {
            const nameElement = document.getElementById('meenoe-name');
            const objectiveElement = document.getElementById('objective-text');
            
            if (nameElement && window.meenoeState.state.name) {
                nameElement.textContent = window.meenoeState.state.name;
            }
            
            if (objectiveElement && window.meenoeState.state.objective) {
                objectiveElement.textContent = window.meenoeState.state.objective;
            }
        }
    }
    
    /**
     * Toggle name editing mode
     */
    toggleNameEdit() {
        const nameElement = document.getElementById('meenoe-name');
        const editButton = document.getElementById('edit-meenoe-name');
        
        if (!nameElement || !editButton) return;
        
        if (this.editingStates.name) {
            this.saveNameEdit();
        } else {
            this.startNameEdit();
        }
    }
    
    /**
     * Start editing the meenoe name
     */
    startNameEdit() {
        const nameElement = document.getElementById('meenoe-name');
        const editButton = document.getElementById('edit-meenoe-name');
        
        if (!nameElement || !editButton) return;
        
        // Store original value
        this.originalName = nameElement.textContent;
        
        // Make element editable
        nameElement.contentEditable = true;
        nameElement.focus();
        nameElement.classList.add('editing');
        
        // Update button
        editButton.innerHTML = '<i class="ti ti-check me-2"></i>Save Title';
        editButton.classList.remove('btn-gradient');
        editButton.classList.add('btn-success');
        
        // Select all text
        this.selectAllText(nameElement);
        
        this.editingStates.name = true;
    }
    
    /**
     * Save name edit
     */
    saveNameEdit() {
        const nameElement = document.getElementById('meenoe-name');
        const editButton = document.getElementById('edit-meenoe-name');
        
        if (!nameElement || !editButton) return;
        
        // Get new value and clean it
        const newName = nameElement.textContent.trim();
        
        // Validate
        if (!newName || newName.length === 0) {
            nameElement.textContent = this.originalName || 'Name Your Meenoe Here';
        } else {
            // Update state
            if (window.meenoeState) {
                window.meenoeState.updateName(newName);
            }
        }
        
        // Reset editing state
        nameElement.contentEditable = false;
        nameElement.classList.remove('editing');
        nameElement.blur();
        
        // Reset button
        editButton.innerHTML = '<i class="ti ti-edit me-2"></i>Edit Meenoe Title';
        editButton.classList.remove('btn-success');
        editButton.classList.add('btn-gradient');
        
        this.editingStates.name = false;
    }
    
    /**
     * Cancel name edit
     */
    cancelNameEdit() {
        const nameElement = document.getElementById('meenoe-name');
        const editButton = document.getElementById('edit-meenoe-name');
        
        if (!nameElement || !editButton) return;
        
        // Restore original value
        nameElement.textContent = this.originalName || 'Name Your Meenoe Here';
        
        // Reset editing state
        nameElement.contentEditable = false;
        nameElement.classList.remove('editing');
        nameElement.blur();
        
        // Reset button
        editButton.innerHTML = '<i class="ti ti-edit me-2"></i>Edit Meenoe Title';
        editButton.classList.remove('btn-success');
        editButton.classList.add('btn-gradient');
        
        this.editingStates.name = false;
    }
    
    /**
     * Toggle objective editing mode
     */
    toggleObjectiveEdit() {
        const objectiveElement = document.getElementById('objective-text');
        const editButton = document.getElementById('edit-objective');
        
        if (!objectiveElement || !editButton) return;
        
        if (this.editingStates.objective) {
            this.saveObjectiveEdit();
        } else {
            this.startObjectiveEdit();
        }
    }
    
    /**
     * Start editing the objective
     */
    startObjectiveEdit() {
        const objectiveElement = document.getElementById('objective-text');
        const editButton = document.getElementById('edit-objective');
        
        if (!objectiveElement || !editButton) return;
        
        // Store original value
        this.originalObjective = objectiveElement.textContent;
        
        // Make element editable
        objectiveElement.contentEditable = true;
        objectiveElement.focus();
        objectiveElement.classList.add('editing');
        
        // Update button
        editButton.innerHTML = '<i class="ti ti-check me-2"></i>';
        editButton.classList.add('text-success');
        
        // Select all text
        this.selectAllText(objectiveElement);
        
        this.editingStates.objective = true;
    }
    
    /**
     * Save objective edit
     */
    saveObjectiveEdit() {
        const objectiveElement = document.getElementById('objective-text');
        const editButton = document.getElementById('edit-objective');
        
        if (!objectiveElement || !editButton) return;
        
        // Get new value and clean it
        const newObjective = objectiveElement.textContent.trim();
        
        // Validate
        if (!newObjective || newObjective.length === 0) {
            objectiveElement.textContent = this.originalObjective || 'Enter your Meenoe objective or an introduction here';
        } else {
            // Update state
            if (window.meenoeState) {
                window.meenoeState.updateObjective(newObjective);
            }
        }
        
        // Reset editing state
        objectiveElement.contentEditable = false;
        objectiveElement.classList.remove('editing');
        objectiveElement.blur();
        
        // Reset button
        editButton.innerHTML = '<i class="ti ti-edit me-2"></i>';
        editButton.classList.remove('text-success');
        
        this.editingStates.objective = false;
    }
    
    /**
     * Cancel objective edit
     */
    cancelObjectiveEdit() {
        const objectiveElement = document.getElementById('objective-text');
        const editButton = document.getElementById('edit-objective');
        
        if (!objectiveElement || !editButton) return;
        
        // Restore original value
        objectiveElement.textContent = this.originalObjective || 'Enter your Meenoe objective or an introduction here';
        
        // Reset editing state
        objectiveElement.contentEditable = false;
        objectiveElement.classList.remove('editing');
        objectiveElement.blur();
        
        // Reset button
        editButton.innerHTML = '<i class="ti ti-edit me-2"></i>';
        editButton.classList.remove('text-success');
        
        this.editingStates.objective = false;
    }
    
    /**
     * Select all text in an element
     */
    selectAllText(element) {
        if (window.getSelection && document.createRange) {
            const range = document.createRange();
            range.selectNodeContents(element);
            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);
        }
    }
    
    /**
     * Reinitialize for dynamic content
     */
    reinitialize() {
        this.initializeElements();
    }
    
    /**
     * Cleanup function
     */
    cleanup() {
        // Reset any editing states
        if (this.editingStates.name) {
            this.cancelNameEdit();
        }
        if (this.editingStates.objective) {
            this.cancelObjectiveEdit();
        }
        
        console.log('🧹 Meenoe Details cleaned up');
    }
}

// Create global instance
window.meenoeDetails = new MeenoeDetails();

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MeenoeDetails;
}
