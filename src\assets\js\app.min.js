document.addEventListener('DOMContentLoaded', function () {
  // Admin Panel settings

  //****************************
  /* This is for the mini-sidebar if width is less then 1170*/
  //****************************
  var setsidebartype = function () {
    var width = window.innerWidth > 0 ? window.innerWidth : screen.width;
    var mainWrapper = document.getElementById('main-wrapper');
    if (width < 1199) {
      mainWrapper.setAttribute('data-sidebartype', 'mini-sidebar');
      mainWrapper.classList.add('mini-sidebar');
    } else {
      mainWrapper.setAttribute('data-sidebartype', 'full');
      mainWrapper.classList.remove('mini-sidebar');
    }
  };

  // Call the function on DOMContentLoaded
  setsidebartype();

  // Add the resize event listener
  window.addEventListener('resize', setsidebartype);

  //****************************
  /* This is for sidebartoggler*/
  //****************************
  var sidebartoggler = document.querySelectorAll('.sidebartoggler');
  
  sidebartoggler.forEach(function (toggler) {
    toggler.addEventListener('click', function () {
      var mainWrapper = document.getElementById('main-wrapper');
      mainWrapper.classList.toggle('mini-sidebar');
      if (mainWrapper.classList.contains('mini-sidebar')) {
        toggler.checked = true;
        mainWrapper.setAttribute('data-sidebartype', 'mini-sidebar');
      } else {
        toggler.checked = false;
        mainWrapper.setAttribute('data-sidebartype', 'full');
      }
    });
  });

  sidebartoggler.forEach(function (toggler) {
    toggler.addEventListener('click', function () {
      var mainWrapper = document.getElementById('main-wrapper');
      mainWrapper.classList.toggle('show-sidebar');
    });
  });

});
