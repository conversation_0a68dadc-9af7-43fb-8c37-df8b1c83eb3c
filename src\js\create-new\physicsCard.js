class SwingingCardManager {
    constructor() {
        // Store physics data for each card
        this.cardData = new WeakMap();
        
        // Physics constants
        this.damping = 0.95;
        this.springStrength = 0.08;
        this.maxAngle = 2; // Maximum swing angle in degrees
        
        this.bindEvents();
    }

    bindEvents() {
        // Use event delegation on document
        document.addEventListener('mouseenter', (e) => {
            const card = e.target.closest('.physicsCard');
            if (card && card.contains(e.target)) {
                this.onMouseEnter({ ...e, target: card });
            }
        }, true);
        
        document.addEventListener('mouseleave', (e) => {
            const card = e.target.closest('.physicsCard');
            if (card && card.contains(e.target)) {
                this.onMouseLeave({ ...e, target: card });
            }
        }, true);
    }

    getCardData(element) {
        if (!this.cardData.has(element)) {
            this.cardData.set(element, {
                angle: 0,
                velocity: 0,
                targetAngle: 0,
                isAnimating: false,
                animationId: null
            });
        }
        return this.cardData.get(element);
    }

    onMouseEnter(e) {
        const element = e.target;
        const data = this.getCardData(element);
        
        const rect = element.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const mouseX = e.clientX;
        
        // Calculate swing direction based on mouse position relative to card center
        const offset = (mouseX - centerX) / (rect.width / 2);
        data.targetAngle = Math.max(-this.maxAngle, Math.min(this.maxAngle, offset * this.maxAngle));
        
        // Add some initial velocity for more dynamic movement
        data.velocity += data.targetAngle * 0.1;
        
        this.startAnimation(element, data);
    }

    onMouseLeave(e) {
        const element = e.target;
        const data = this.getCardData(element);
        
        data.targetAngle = 0;
        // Let it swing back to center naturally
    }

    startAnimation(element, data) {
        if (data.isAnimating) return;
        
        data.isAnimating = true;
        this.animate(element, data);
    }

    animate(element, data) {
        // Spring physics to target angle
        const force = (data.targetAngle - data.angle) * this.springStrength;
        data.velocity += force;
        
        // Apply damping
        data.velocity *= this.damping;
        
        // Update angle
        data.angle += data.velocity;
        
        // Apply rotation
        element.style.transform = `rotate(${data.angle}deg)`;
        
        // Continue animation if there's still movement
        if (Math.abs(data.velocity) > 0.01 || Math.abs(data.angle - data.targetAngle) > 0.1) {
            data.animationId = requestAnimationFrame(() => this.animate(element, data));
        } else {
            // Snap to final position and stop
            data.angle = data.targetAngle;
            data.velocity = 0;
            element.style.transform = `rotate(${data.angle}deg)`;
            data.isAnimating = false;
        }
    }
    }

    // Initialize the physics manager
    document.addEventListener('DOMContentLoaded', () => {
    new SwingingCardManager();
    });
